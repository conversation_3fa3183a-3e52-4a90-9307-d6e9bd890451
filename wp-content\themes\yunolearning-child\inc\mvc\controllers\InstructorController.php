<?php

namespace V4;

/**
 *  Instructor Controller
 */


class InstructorController extends Controller
{

    /**
     * Constructor to initialize the InstructorController
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');
        $this->loadModel('user');
        $this->loadModel('instructor');
        $this->loadFilter('instructor');
    }

    /**
     * Private function to handle authentication and authorization check for instructor endpoints
     * Only allows yuno-admin, org-admin, and admin roles (excludes learner and instructor)
     * 
     * @param \WP_REST_Request $request The request object
     * @return array|null Returns error response array if authentication fails, null if successful
     */
    private function authenticateAndAuthorize($request)
    {
        $userId = 0;
        $authToken = $request->get_header('authorization');

        // Check if authorization token is present
        if (empty($authToken)) {
            return $this->response->error('TOKEN_FAIL', ['message' => 'Authorization token not found.']);
        }

        // Check token format and extract token
        if (stripos($authToken, 'bearer ') !== 0) {
            return $this->response->error('TOKEN_FAIL', ['message' => 'Invalid token format. Missing Bearer prefix.']);
        }

        list(, $cleanToken) = explode(" ", $authToken, 2);
        if (empty($cleanToken)) {
            return $this->response->error('TOKEN_FAIL', ['message' => 'Token is empty after Bearer.']);
        }

        // Try jwt_token_validation_check function first
        if (function_exists('jwt_token_validation_check')) {
            $validationResult = jwt_token_validation_check($cleanToken);
            
            if (is_numeric($validationResult) && $validationResult > 0) {
                $userId = (int) $validationResult;
            } elseif ($validationResult === true && function_exists('token_validation_check')) {
                // Fallback to token_validation_check
                $userIdFromTvCheck = token_validation_check($cleanToken);
                if (is_numeric($userIdFromTvCheck) && $userIdFromTvCheck > 0) {
                    $userId = (int) $userIdFromTvCheck;
                }
            }
        }
        
        // Manual JWT token decoding as last resort
        if (!($userId > 0)) {
            try {
                $tokenParts = explode('.', $cleanToken);
                if (count($tokenParts) === 3) {
                    // Decode the payload (second part)
                    $payloadEncoded = $tokenParts[1];
                    
                    // Add padding if necessary for base64 decoding
                    $paddingLength = 4 - (strlen($payloadEncoded) % 4);
                    if ($paddingLength !== 4) {
                        $payloadEncoded .= str_repeat('=', $paddingLength);
                    }
                    
                    $payloadJson = base64_decode(strtr($payloadEncoded, '-_', '+/'));
                    $payload = json_decode($payloadJson, true);
                    
                    if ($payload && is_array($payload)) {
                        // Try different possible user ID fields
                        $possibleUserIdFields = ['userId', 'user_id', 'sub', 'id', 'uid'];
                        
                        foreach ($possibleUserIdFields as $field) {
                            if (isset($payload[$field]) && is_numeric($payload[$field]) && $payload[$field] > 0) {
                                $userId = (int) $payload[$field];
                                break;
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                // Token decoding failed, $userId remains 0
            }
        }

        if (!($userId > 0)) {
            return $this->response->error('USER_ID_FAIL', ['message' => 'Could not determine User ID from token.']);
        }

        // Get user role
        $userRole = $this->userModel->getUserRole($userId);
        if (!$userRole || $userRole === 'unknown' || $userRole === false) {
            return $this->response->error('ROLE_FAIL', ['message' => 'Could not determine user role.']);
        }

        // Check if user has authorized role (only yuno-admin, org-admin, and admin - excludes learner and instructor)
        $allowedRoles = ['yuno-admin', 'org-admin', 'admin'];
        if (!in_array($userRole, $allowedRoles)) {
            return $this->response->error('ACCESS_DENIED', [
                'message' => 'You are not authorized to access this resource. Required roles: ' . implode(', ', $allowedRoles) . '. Your role: ' . $userRole
            ]);
        }

        // Authentication and authorization successful
        return null;
    }

    /**
     * Fetches the working hours for a given instructor resource.
     * If no valid working hours data is found, returns a default 7-day blank structure.
     *
     * @param array $request Request payload containing 'resourceType' and 'resourceId'
     * @return WP_REST_Response JSON-formatted response with working hours data
     */
    public function getResourceWorkingHours($request)
    {
        try {
            $workingHours = $this->instructorModel->getInstructorWorkingHours(
                ['resource' => $request['resourceType'], 'id' => $request['resourceId']]
            );

            $isInvalid = !is_array($workingHours)
                || !isset($workingHours['resource'])
                || !isset($workingHours['days'])
                || !is_array($workingHours['days'])
                || empty($workingHours['days'])
                || count(array_filter($workingHours['days'], fn($day) => $day !== 'Error::Key_Not_Found_In_Data')) === 0;

            if ($isInvalid || !$workingHours) {
                return $this->response->error("GET_FAIL", ['message' => "No Working Hours found"]);
            }

            return $this->response->success("GET_SUCCESS", $workingHours, ['message' => "Working Hours found"]);

        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', [
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }

    /**
     * Retrieves detailed information about an instructor
     *
     * @param array $request Request payload containing 'instructorId'
     * @return WP_REST_Response JSON-formatted response with instructor details
     */
    public function getInstructorDetails($request)
    {
        try {
            $instructorId = (int)$request['instructorId'];
            
            if (empty($instructorId) || !is_numeric($instructorId)) {
                return $this->response->error('VALIDATION_FAIL', (object)['message' => "Invalid instructor ID"]);
            }

            // Prepare filter parameter for schema validation
            $schemaFilter = [];
            if ($request->get_param('schema')) {
                $schemaFilter['schema'] = $request->get_param('schema');
            }
            
            $instructorDetails = $this->instructorModel->getInstructor(['id' => $instructorId], $schemaFilter);
            
            if (!$instructorDetails) {
                return $this->response->error("GET_FAIL", ['message' => "Instructor details not found"]);
            }
            
            return $this->response->success("GET_SUCCESS", $instructorDetails, ['message' => "Instructor details found"]);
            
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', [
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }

    public function getResourceAvailability($request)
    {
        try {
            $resource_id = (int)$request['resource_id'];
            $start_date = $request['start_date'];
            $end_date = $request['end_date'];
            $start_time = $request['start_time'];
            $end_time = $request['end_time'];
            $resource = $request['resource'] ?? 'Instructor';
            $isAvailable = $this->instructorModel->getInstructorAvailability(['resource' => $resource, 'resource_id' => $resource_id, 'start_date' => $start_date, 'end_date' => $end_date, 'start_time' => $start_time, 'end_time' => $end_time], ['schema' => 'Availability']);

            if (!$isAvailable) {
                return $this->response->error("GET_FAIL", (object)['message' => "Resource not available"]);
            }
            
            return $this->response->success("GET_SUCCESS", (object)$isAvailable, ['message' => "Resource is available"] );
            
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', (object)['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    /**
     * Retrieves a list of instructors with filtering options
     *
     * @param array $request Request payload containing optional filters
     * @return \WP_REST_Response JSON-formatted response with instructors data
     */
    public function getInstructors($request)
    {
        try {
            // Check authentication and authorization
            $authResult = $this->authenticateAndAuthorize($request);
            if ($authResult !== null) {
                return $authResult;
            }

            error_log('getInstructors method called');
            
            $getVars = $request->get_query_params();
            $viewType = $request->get_param('viewType') ?? 'grid'; // Get viewType, default to grid
            error_log('View Type: ' . $viewType);
            error_log('Query params: ' . print_r($getVars, true));
            
            $userId = isset($getVars['user_id']) ? (int)$getVars['user_id'] : 0;

            $categoryIds = [];
            if (isset($getVars['category_ids'])) {
                if (is_array($getVars['category_ids'])) {
                    $categoryIds = array_map('intval', $getVars['category_ids']);
                } else if (is_string($getVars['category_ids'])) {
                    if (preg_match('/^\[(.*)\]$/', $getVars['category_ids'], $matches)) {
                        $categoryIds = array_map('intval', explode(',', $matches[1]));
                    } else if (strpos($getVars['category_ids'], ',') !== false) {
                        $categoryIds = array_map('intval', explode(',', $getVars['category_ids']));
                    } else {
                        $categoryIds = [(int)$getVars['category_ids']];
                    }
                }
            }
            error_log('Processed category_ids: ' . print_r($categoryIds, true));

            $isFeatured = isset($getVars['is_featured']) ? filter_var($getVars['is_featured'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;
            // Ensure boolean conversion for these filters, defaulting to appropriate values or null
            $hasActiveBatches = isset($getVars['has_active_batches']) ? filter_var($getVars['has_active_batches'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;
            $hasPastBatches = isset($getVars['has_past_batches']) ? filter_var($getVars['has_past_batches'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;
            $hasActiveEnrollments = isset($getVars['has_active_enrollments']) ? filter_var($getVars['has_active_enrollments'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : false;
            $hasPastEnrollments = isset($getVars['has_past_enrollments']) ? filter_var($getVars['has_past_enrollments'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : false;
            $isDisabled = isset($getVars['is_disabled']) ? filter_var($getVars['is_disabled'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : false;
            $orgId = isset($getVars['org_id']) ? (int)$getVars['org_id'] : 0;
            $hasMappedCourses = isset($getVars['has_mapped_courses']) ? filter_var($getVars['has_mapped_courses'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;
            $hasWorkingHours = isset($getVars['has_working_hours']) ? filter_var($getVars['has_working_hours'], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;

            $filters = [
                'viewType' => $viewType,
                'user_id' => $userId,
                'category_ids' => $categoryIds,
                'is_featured' => $isFeatured,
                'has_active_batches' => $hasActiveBatches,
                'has_past_batches' => $hasPastBatches,
                'has_active_enrollments' => $hasActiveEnrollments,
                'has_past_enrollments' => $hasPastEnrollments,
                'is_disabled' => $isDisabled,
                'org_id' => $orgId,
                'has_mapped_courses' => $hasMappedCourses,
                'has_working_hours' => $hasWorkingHours,
                'status' => $getVars['status'] ?? null,
                'vc_status' => $getVars['vc_status'] ?? null,
                'native_language' => $getVars['native_language'] ?? null,
                'avg_rating' => $getVars['avg_rating'] ?? 0,
                'days' => $getVars['days'] ?? '0',
                'is_completed' => $getVars['is_completed'] ?? 'yes',
                'batch_id' => isset($getVars['batch_id']) ? (int)$getVars['batch_id'] : 0,
                'enrollment_id' => isset($getVars['enrollment_id']) ? (int)$getVars['enrollment_id'] : 0,
                'mapped_courses' => isset($getVars['mapped_courses']) ? (is_array($getVars['mapped_courses']) ? array_map('intval', $getVars['mapped_courses']) : [(int)$getVars['mapped_courses']]) : [],
            ];
            
            if (isset($getVars['limit']) && isset($getVars['offset'])) {
                $filters['limit'] = (int)$getVars['limit'];
                $filters['offset'] = (int)$getVars['offset'];
            }

            error_log('Filters passed to controller: ' . print_r($filters, true));

            // 1. Perform authorization check (moved from model)
            $authError = $this->performAuthorizationCheck($filters);
            if ($authError) {
                return new \WP_REST_Response($authError, 200);
            }

            // 2. Parse and validate filters (moved from model)
            $parsedFilters = $this->parseAndValidateFilters($filters);

            // 3. Build Elasticsearch query (moved from model)
            $customQuery = $this->buildElasticsearchQuery($parsedFilters);

            // 4. Prepare query string for pagination
            $queryString = [
                "from" => $parsedFilters['offset'],
                "size" => $parsedFilters['limit']
            ];

            // 5. Prepare filter parameter for schema validation
            $schemaFilter = [];
            if ($request->get_param('schema')) {
                $schemaFilter['schema'] = $request->get_param('schema');
            } else {
                // Default to Instructor_Basic schema for getInstructors
                $schemaFilter['schema'] = 'Instructor_Basic';
            }

            // 6. Prepare query array following EnrollmentController pattern
            $query = [
                'custom' => $customQuery,
                'qryStr' => $queryString,
                'filters' => $filters,  // Pass original filters for backward compatibility
                'parsedFilters' => $parsedFilters  // Pass parsed filters for processing
            ];

            error_log('ES Query built in controller: ' . json_encode($customQuery));

            // 7. Call model with pre-built query
            $instructors = $this->instructorModel->getInstructors($query, $schemaFilter);
            error_log('Instructors result from model: ' . ($instructors ? 'Data found (' . ($instructors['count'] ?? 'N/A') . ')' : 'No data found'));
            
            if (!$instructors || !isset($instructors['status']) || $instructors['status'] === 'FAIL') {
                $codes = error_code_setting();
                error_log('No instructors found or error from model, returning 200 with error message.');
                return new \WP_REST_Response([
                    'code' => $instructors['code'] ?? $codes["GET_FAIL"]["code"],
                    'message' => $instructors['message'] ?? 'No Data Found',
                    'status' => $instructors['status'] ?? $codes["GET_FAIL"]["status"],
                    'count' => $instructors['count'] ?? 0,
                    'data' => $instructors['data'] ?? [] 
                ], 200);
            }
            
            error_log('Returning success response with data from model');
            return new \WP_REST_Response($instructors, 200);
            
        } catch (\Exception $e) {
            error_log('Exception in getInstructors: ' . $e->getMessage());
            $codes = error_code_setting();
            return new \WP_REST_Response([
                'code' => $codes["GET_FAIL"]["code"],
                'message' => $e->getMessage(),
                'status' => $codes["GET_FAIL"]["status"]
            ], 200);
        }
    }

    /**
     * Retrieves filters for instructors.
     *
     * @param \WP_REST_Request $request Request object.
     * @return \WP_REST_Response JSON-formatted response with instructor filters.
     */
    public function getInstructorFilters($request)
    {
        try {
            // Check authentication and authorization
            $authResult = $this->authenticateAndAuthorize($request);
            if ($authResult !== null) {
                return $authResult;
            }

            // Log the request, similar to getEnrollmentFilters
            if (function_exists('ynLog')) {
                ynLog("getInstructorFilters - Request received: " . json_encode($request->get_params()), 'getInstructorFilters');
            }

            $getVars = $request->get_query_params();
            if (isset($getVars['limit']) && isset($getVars['offset'])) {
                $limit = (int)$getVars['limit'];
                $offset = (int)$getVars['offset'];
            } else {
                $limit = YN_DEFAULT_LIMIT;
                $offset = YN_DEFAULT_OFFSET;
            }

            $filters = [];

            // Featured Filter
            $selectedFeatured = $request->get_param('is_featured') ?? "0";
            $featuredFilterData = $this->instructorFilter->generateFeaturedFilterData($selectedFeatured);
            if ($featuredFilterData) {
                $filters[] = $featuredFilterData;
            }

            // Disabled Filter
            $selectedDisabled = $request->get_param('is_disabled') ?? "0";
            $disabledFilterData = $this->instructorFilter->generateDisabledFilterData($selectedDisabled);
            if ($disabledFilterData) {
                $filters[] = $disabledFilterData;
            }

            // Category Filter
            $categoryFilterData = $this->instructorFilter->generateCategoryFilterData($request, $limit, $offset);
            if ($categoryFilterData) {
                $filters[] = $categoryFilterData;
            }

            // Organization Filter
            $organizationFilterData = $this->instructorFilter->generateOrganizationFilterData($request, $limit, $offset);
            if ($organizationFilterData) {
                $filters[] = $organizationFilterData;
            }

            // Has Mapped Courses Filter
            $selectedMappedCourses = $request->get_param('has_mapped_courses') ?? "0";
            $mappedCoursesFilterData = $this->instructorFilter->generateHasMappedCoursesFilterData($selectedMappedCourses);
            if ($mappedCoursesFilterData) {
                $filters[] = $mappedCoursesFilterData;
            }

            // Has Active Batches Filter
            $selectedActiveBatches = $request->get_param('has_active_batches') ?? "0";
            $activeBatchesFilterData = $this->instructorFilter->generateHasActiveBatchesFilterData($selectedActiveBatches);
            if ($activeBatchesFilterData) {
                $filters[] = $activeBatchesFilterData;
            }

            // Has Past Batches Filter
            $selectedPastBatches = $request->get_param('has_past_batches') ?? "0";
            $pastBatchesFilterData = $this->instructorFilter->generateHasPastBatchesFilterData($selectedPastBatches);
            if ($pastBatchesFilterData) {
                $filters[] = $pastBatchesFilterData;
            }

            // Has Active Enrollments Filter
            $selectedActiveEnrollments = $request->get_param('has_active_enrollments') ?? "0";
            $activeEnrollmentsFilterData = $this->instructorFilter->generateHasActiveEnrollmentsFilterData($selectedActiveEnrollments);
            if ($activeEnrollmentsFilterData) {
                $filters[] = $activeEnrollmentsFilterData;
            }

            // Has Past Enrollments Filter
            $selectedPastEnrollments = $request->get_param('has_past_enrollments') ?? "0";
            $pastEnrollmentsFilterData = $this->instructorFilter->generateHasPastEnrollmentsFilterData($selectedPastEnrollments);
            if ($pastEnrollmentsFilterData) {
                $filters[] = $pastEnrollmentsFilterData;
            }

            // Has Working Hours Filter
            $selectedWorkingHours = $request->get_param('has_working_hours') ?? "0";
            $workingHoursFilterData = $this->instructorFilter->generateHasWorkingHoursFilterData($selectedWorkingHours);
            if ($workingHoursFilterData) {
                $filters[] = $workingHoursFilterData;
            }
            
            if (empty($filters)) {
                 return $this->response->error("GET_FAIL", ['message' => "No filters found."]);
            }
            
            return $this->response->success("GET_SUCCESS", $filters, ['message' => "Instructor filters fetched successfully."]);

        } catch (\Exception $e) {
            // Log the exception if ynLog is available
            if (function_exists('ynLog')) {
                ynLog("getInstructorFilters - Exception: " . $e->getMessage(), 'getInstructorFilters_Error');
            }
            return $this->response->error('GET_FAIL', [
                'message' => isset($this->common) ? $this->common->globalExceptionMessage($e) : $e->getMessage()
            ]);
        }
    }

    public function getInstructorVirtualClasserooms($request) {
        try {
            $instructorId = (int)$request['instructorId'];
            $getVars = $request->get_query_params();
            $validation_checks = [
                'instructorId' => 'numeric'
            ];
            
            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired(['instructorId' => $instructorId], $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // $userData = $this->userModel->validUser($instructorId);
            // if (!$userData) {
            //     return $this->response->error('USER_FAIL');
            // }
            // GET ES record with payload
            $userData = $this->userModel->getUser($instructorId);
           
            if (!$userData) {
                return $this->response->error('USER_FAIL');
            }

            if ($userData===false || $this->userModel->checkRole($userData['role'],$this->userModel->yn_Instructor)===false) {
                return $this->response->error('ROLE_FAIL');
            }

            if (isset($instructorId)) {
                $query['params'] = [
                    'instructorId' => $instructorId
                ];

                $query['custom'] = [
                    "size" => 0,
                    "query" => [
                        "nested" => [
                            "path" => "data.details",
                            "query" => [
                                "term" => [
                                    "data.details.mapped_instructor_ids" => $instructorId
                                ]
                            ]
                        ]
                    ],
                    "aggs" => [
                        "distinct_org_ids" => [
                            "nested" => [
                                "path" => "data.details"
                            ],
                            "aggs" => [
                                "org_ids" => [
                                    "composite" => [
                                        //"size" => ELASTIC_RECORDS_COUNT, // Number of results per page
                                        "sources" => [
                                            [
                                                "org_id" => [
                                                    "terms" => [
                                                        "field" => "data.details.org_id"
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    "aggs" => [
                                        "sample_field" => [
                                            "top_hits" => [
                                                "_source" => [
                                                    "includes" => [
                                                        "data.details.academies",
                                                        "data.details.mapped_instructor_ids",
                                                        "data.details.org_id"
                                                    ]
                                                ],
                                                "size" => 1
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "_source" => false // Excludes document source in the results
                ];
            }

            $virtualClasses = $this->instructorModel->getInstructorVirtualClasserooms($query);
			
            if (!$virtualClasses) {
                return $this->response->error("GET_FAIL", ['replace'=>'VirtualClasses']);
            }
            return $this->response->success("GET_SUCCESS", $virtualClasses, ['replace'=>'VirtualClasses'] );
            
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)] );
        }
    }

    public function getInstructorAvailableSlots($request)
    {
        try {
            $params = $request->get_query_params();

            $resourceIds = isset($params['resource_id']) 
                ? (is_array($params['resource_id']) 
                    ? array_map('intval', $params['resource_id']) 
                    : array_map('intval', explode(',', $params['resource_id'])))
                : [];

            $startDate = $params['start_date'] ?? '';
            $endDate = $params['end_date'] ?? '';
            $startTime = $params['start_time'] ?? '00:00';
            $endTime = $params['end_time'] ?? '23:59';
            $today = date('Y-m-d');

            $startTime = ($startDate === $today) 
                ? ($params['start_time'] ?? '00:00') 
                : '00:00';

            if (empty($resourceIds) || !$startDate || !$endDate) {
                return $this->response->error('GET_FAIL', ['message' => 'Missing or invalid query parameters']);
            }

            $days = $this->instructorModel->getClassDays($startDate, $endDate);

            $workingInstructors = $this->instructorModel->checkInstructorsWorkingOnDays($resourceIds, $days);
            $workingInstructorIds = $workingInstructors['data']['resource_ids'] ?? [];

            if (empty($workingInstructorIds)) {
                return $this->response->error('GET_FAIL', ['message' => 'Instructor not working in selected date range']);
            }

            $availability = $this->instructorModel->getAvailabilityOfInstructor(
                $workingInstructorIds,
                $startDate,
                $endDate,
                $startTime,
                $endTime
            );

            if (!empty($availability)) {
                $hasSlots = false;
                foreach ($availability['time_slots'] as $slotData) {
                    if (!empty($slotData['slots'])) {
                        $hasSlots = true;
                        break;
                    }
                }

                return $this->response->success(
                    $hasSlots ? 'GET_SUCCESS' : 'GET_FAIL',
                    ['available_slots' => $availability],
                    ['message' => $hasSlots ? 'Slots found' : 'No available slots found']
                );
            }

            return $this->response->error('GET_FAIL', ['message' => 'No calendar availability found']);

        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }

    /**
     * Get mapped courses for a specific instructor
     * Only accessible by org-admin and yuno-admin
     *
     */
    public function getInstructorMappedCourses($request)
    {
        try {
            // Authorization check - only org-admin and yuno-admin can access
            $authResult = $this->authenticateAndAuthorize($request);
            if (is_wp_error($authResult)) {
                return $authResult;
            }

            $instructorId = $request['instructorId'];
            
            if (empty($instructorId) || !is_numeric($instructorId)) {
                return $this->response->error('VALIDATION_FAIL', ['message' => "Invalid instructor ID"]);
            }

            // Prepare filter parameter for schema validation
            $schemaFilter = [];
            if ($request->get_param('schema')) {
                $schemaFilter['schema'] = $request->get_param('schema');
            } else {
                $schemaFilter['schema'] = 'Course_Minimal';
            }
            
            $mappedCourses = $this->instructorModel->getInstructorMappedCourses(['id' => $instructorId], $schemaFilter);
            
            if (!$mappedCourses) {
                return $this->response->error("GET_FAIL", ['message' => "No mapped courses found for this instructor"]);
            }
            
            return $this->response->success("GET_SUCCESS", $mappedCourses, ['message' => "Instructor mapped courses found"]);
            
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', [
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }

    /**
     * Creates a new instructor or converts an existing user to an instructor
     *
     * @param array $request Request payload containing instructor data
     * @return WP_REST_Response JSON-formatted response with creation result
     */
    public function createInstructor($request)
    {
        try {
            $data = json_decode($request->get_body(), true);
            
            // Validate request data
            if (empty($data)) {
                return $this->response->error('VALIDATION_FAIL', ['message' => "Invalid request data"]);
            }
            
            // Check if we have either user_id or email
            if (empty($data['user_id']) && empty($data['email'])) {
                return $this->response->error('VALIDATION_FAIL', ['message' => "Either user_id or email is required"]);
            }
            
            // If creating new user, validate required fields
            if (empty($data['user_id']) && !empty($data['email'])) {
                $requiredFields = ['email', 'first_name', 'last_name', 'phone'];
                foreach ($requiredFields as $field) {
                    if (empty($data[$field])) {
                        return $this->response->error('VALIDATION_FAIL', ['message' => "$field is required for new users"]);
                    }
                }
            }
            
            // Create instructor
            $result = $this->instructorModel->createInstructor($data);
            
            if (is_wp_error($result)) {
                return $this->response->error('POST_INSERT_FAIL', (object)[
                    'message' => $result->get_error_message()
                ]);
            }
            
            return $this->response->success("POST_INSERT", $result, ['message' => "Instructor created successfully"]);
            
        } catch (\Exception $e) {
            return $this->response->error('POST_INSERT_FAIL', (object)[
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }

    /**
     * Checks if the user specified in filters has the required admin role.
     */
    private function performAuthorizationCheck(array $filters): ?array
    {
        if (empty($filters['user_id'])) {
            return null;
        }

        $userId = (int) $filters['user_id'];
        $userRole = $this->userModel->getUserRole($userId);

        if (!$userRole) {
            return $this->createErrorResponse(404, 'User not found for the provided user_id.');
        }

        $isOrgAdmin = $this->userModel->checkRole($userRole, $this->userModel->yn_Org_Admin);
        $isYunoAdmin = $this->userModel->checkRole($userRole, $this->userModel->yn_Yuno_Admin);

        if ($isOrgAdmin === false && $isYunoAdmin === false) {
            return $this->createErrorResponse(403, 'Access Denied: User does not have the required role.');
        }
        return null;
    }

    /**
     * Parses, validates, and standardizes the input filter array.
     */
    private function parseAndValidateFilters(array $filters): array
    {
        $booleanOrNull = fn ($key) => isset($filters[$key]) ? filter_var($filters[$key], FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) : null;

        $parsed = [
            'status' => $filters['status'] ?? null,
            'vc_status' => $filters['vc_status'] ?? null,
            'native_language' => $filters['native_language'] ?? null,
            'avg_rating' => (float) ($filters['avg_rating'] ?? 0),
            'course_id' => (int) ($filters['course_id'] ?? 0),
            'category_id' => (int) ($filters['category_id'] ?? 0),
            'category_ids' => $this->parseIdList($filters['category_ids'] ?? []),
            'mapped_courses' => $this->parseIdList($filters['mapped_courses'] ?? []),
            'is_featured' => $booleanOrNull('is_featured'),
            'has_active_batches' => $booleanOrNull('has_active_batches'),
            'has_past_batches' => $booleanOrNull('has_past_batches'),
            'has_active_enrollments' => $booleanOrNull('has_active_enrollments'),
            'has_past_enrollments' => $booleanOrNull('has_past_enrollments'),
            'is_disabled' => $booleanOrNull('is_disabled'),
            'org_id' => (int) ($filters['org_id'] ?? 0),
            'has_mapped_courses' => $booleanOrNull('has_mapped_courses'),
            'has_working_hours' => $booleanOrNull('has_working_hours'),
            'limit' => max(1, (int) ($filters['limit'] ?? 10)),
            'offset' => max(0, (int) ($filters['offset'] ?? 0)),
            'viewType' => $filters['viewType'] ?? 'grid',
            'user_id' => (int) ($filters['user_id'] ?? 0),
            'batch_id' => (int) ($filters['batch_id'] ?? 0),
            'enrollment_id' => (int) ($filters['enrollment_id'] ?? 0),
            'days' => $filters['days'] ?? '0',
            'is_completed' => $filters['is_completed'] ?? 'yes'
        ];

        return $parsed;
    }

    /**
     * Helper to parse a comma-separated string or array of IDs into a clean integer array.
     */
    private function parseIdList($value): array
    {
        if (is_array($value)) {
            return array_filter(array_map('intval', $value));
        }
        if (is_string($value) && !empty($value)) {
            return array_filter(array_map('intval', explode(',', trim($value, '[] '))));
        }
        return [];
    }

    /**
     * Builds the Elasticsearch query body based on parsed filters.
     */
    private function buildElasticsearchQuery(array $parsedFilters): array
    {
        $must = [];
        if ($parsedFilters['is_disabled'] === true) {
            $must[] = ["bool" => ["must_not" => [["match" => ["data.details.account_login_status.keyword" => 'active']]]]];
        }
        if ($parsedFilters['status']) {
            $statusValue = ($parsedFilters['status'] === 'disabled') ? 'de-active' : $parsedFilters['status'];
            $must[] = ["match" => ["data.details.account_login_status.keyword" => $statusValue]];
        }
        if ($parsedFilters['vc_status'] && $parsedFilters['vc_status'] !== 'all') {
            $must[] = ["match" => ["data.details.vc_status" => $parsedFilters['vc_status']]];
        }
        if ($parsedFilters['native_language'] && $parsedFilters['native_language'] !== 'all') {
            $must[] = ["match" => ["data.details.native_language" => urldecode($parsedFilters['native_language'])]];
        }
        if ($parsedFilters['course_id'] > 0) {
            $must[] = ["term" => ["data.details.mapped_courses" => $parsedFilters['course_id']]];
        }
        if ($parsedFilters['category_id'] > 0) {
            $must[] = ["term" => ["data.details.mapped_categories" => $parsedFilters['category_id']]];
        }
        if (!empty($parsedFilters['category_ids'])) {
            $should = array_map(fn ($id) => ["term" => ["data.details.mapped_categories" => $id]], $parsedFilters['category_ids']);
            $must[] = ["bool" => ["should" => $should, "minimum_should_match" => count($should)]];
        }
        if (!empty($parsedFilters['mapped_courses'])) {
            $must[] = ["terms" => ["data.details.mapped_courses" => $parsedFilters['mapped_courses']]];
        }
        if ($parsedFilters['is_featured'] === true) {
            $must[] = ["bool" => ["should" => [["term" => ["data.details.is_featured" => true]], ["term" => ["data.details.is_featured" => "true"]], ["term" => ["data.details.is_featured" => 1]], ["term" => ["data.details.is_featured" => "1"]]], "minimum_should_match" => 1]];
        }
        if ($parsedFilters['avg_rating'] > 0) {
            $must[] = ["range" => ["data.details.avg_rating" => ["gte" => $parsedFilters['avg_rating']]]];
        }
        if ($parsedFilters['has_mapped_courses'] === true) {
            $must[] = ["exists" => ["field" => "data.details.mapped_courses"]];
        }

        return [
            "query" => ["bool" => ["must" => $must]],
            "_source" => ["data.details.user_id", "data.details.first_name", "data.details.last_name", "data.details.mapped_courses", "data.details.mapped_categories", "data.details.working_hours", "data.details.account_login_status", "data.details.native_language", "data.details.learner_avg_class_rating", "data.details.reviews_count", "data.details.vc_status"]
        ];
    }

    /**
     * Creates an error response array.
     */
    private function createErrorResponse(int $code, string $message): array
    {
        return ['code' => $code, 'message' => $message, 'status' => 'FAIL', 'count' => 0, 'data' => []];
    }
}
