# getInstructors Refactoring Plan

## Current Issues:
1. ES queries are built and executed in InstructorModel
2. Complex filtering logic mixed with ES query building
3. Multiple ES index queries scattered throughout the model
4. Difficult to maintain and debug ES queries

## Target Architecture (Following EnrollmentController <PERSON>tern):

### **Phase 1: Move ES Query Building to Controller**

#### **InstructorController.php Changes:**
```php
public function getInstructors($request)
{
    try {
        // 1. Authentication & Authorization (keep existing)
        $authResult = $this->authenticateAndAuthorize($request);
        if ($authResult !== null) {
            return $authResult;
        }

        // 2. Extract and Parse Parameters (enhanced)
        $filters = $this->extractAndValidateFilters($request);
        
        // 3. Build Primary ES Query (NEW - moved from model)
        $primaryESQuery = $this->buildPrimaryInstructorQuery($filters);
        
        // 4. Execute Primary ES Query (NEW)
        $primaryResults = $this->executeInstructorQuery($primaryESQuery);
        
        if (empty($primaryResults)) {
            return new \WP_REST_Response($this->createEmptyResponse(), 200);
        }
        
        // 5. Build Secondary ES Queries for Complex Filters (NEW)
        $secondaryQueries = $this->buildSecondaryQueries($filters, $primaryResults);
        
        // 6. Execute Secondary Queries (NEW)
        $secondaryResults = $this->executeSecondaryQueries($secondaryQueries);
        
        // 7. Pass Structured Data to Model (MODIFIED)
        $modelData = [
            'primary_results' => $primaryResults,
            'secondary_results' => $secondaryResults,
            'filters' => $filters,
            'schema' => $this->prepareSchemaFilter($request)
        ];
        
        // 8. Model Processes Data (business logic only)
        $instructors = $this->instructorModel->processInstructorData($modelData);
        
        // 9. Return Response (keep existing)
        if (!$instructors || $instructors['status'] === 'FAIL') {
            // ... existing error handling
        }
        
        return new \WP_REST_Response($instructors, 200);
        
    } catch (\Exception $e) {
        // ... existing exception handling
    }
}
```

#### **New Controller Methods:**
```php
private function extractAndValidateFilters($request): array
{
    // Extract all filters with validation
    // Similar to EnrollmentController parameter extraction
}

private function buildPrimaryInstructorQuery(array $filters): array
{
    // Build main instructorsignedup query
    // Move logic from InstructorModel::buildElasticsearchQuery
}

private function executeInstructorQuery(array $query): ?array
{
    // Execute ES query using $this->es library
    // Handle errors and logging
}

private function buildSecondaryQueries(array $filters, array $primaryResults): array
{
    // Build queries for course, academies, batchenrollmentevent indices
    // Only if needed based on filters
}

private function executeSecondaryQueries(array $queries): array
{
    // Execute multiple ES queries efficiently
    // Return structured results
}
```

### **Phase 2: Simplify InstructorModel**

#### **InstructorModel.php Changes:**
```php
public function processInstructorData(array $modelData): array
{
    // 1. Extract candidates from primary ES results
    $candidates = $this->extractCandidatesFromResults($modelData['primary_results']);
    
    // 2. Filter candidates with valid WordPress users
    $candidates = $this->filterCandidatesWithValidUsers($candidates);
    
    // 3. Apply PHP-based filtering using secondary results
    $filteredCandidates = $this->performPostFiltering(
        $candidates, 
        $modelData['filters'], 
        $modelData['secondary_results']
    );
    
    // 4. Sort, paginate, enrich (keep existing logic)
    $sortedCandidates = $this->sortCandidatesByName($filteredCandidates);
    $totalCount = count($sortedCandidates);
    $paginatedCandidates = array_slice($sortedCandidates, $offset, $limit);
    $enrichedInstructors = $this->enrichPaginatedInstructors($paginatedCandidates, $filters, $schema);
    
    // 5. Format response
    return $this->formatFinalResponse($enrichedInstructors, $totalCount, $viewType);
}
```

### **Phase 3: ES Query Consolidation**

#### **Consolidate ES Queries:**
1. **Primary Query:** `instructorsignedup` index
2. **Batch Queries:** `course` index (for active/past batches)
3. **Org Queries:** `academies` + `course` indices (for org affiliation)
4. **Enrollment Queries:** `batchenrollmentevent` index

#### **Query Execution Strategy:**
```php
private function executeSecondaryQueries(array $queries): array
{
    $results = [];
    
    // Execute batch queries if needed
    if (isset($queries['batch_queries'])) {
        $results['batch_data'] = $this->executeBatchQueries($queries['batch_queries']);
    }
    
    // Execute org queries if needed
    if (isset($queries['org_queries'])) {
        $results['org_data'] = $this->executeOrgQueries($queries['org_queries']);
    }
    
    // Execute enrollment queries if needed
    if (isset($queries['enrollment_queries'])) {
        $results['enrollment_data'] = $this->executeEnrollmentQueries($queries['enrollment_queries']);
    }
    
    return $results;
}
```

## **Benefits of This Refactoring:**

1. **Separation of Concerns:** Controller handles ES queries, Model handles business logic
2. **Maintainability:** ES queries centralized and easier to debug
3. **Performance:** Better query optimization and caching opportunities
4. **Consistency:** Follows established EnrollmentController pattern
5. **Testability:** Easier to unit test individual components
6. **Debugging:** Clear separation makes debugging ES issues easier

## **Migration Strategy:**

1. **Phase 1:** Create new controller methods alongside existing code
2. **Phase 2:** Gradually move ES query logic from model to controller
3. **Phase 3:** Simplify model methods to focus on data processing
4. **Phase 4:** Remove old ES query methods from model
5. **Phase 5:** Add comprehensive testing and validation

## **Risk Mitigation:**

1. **Backward Compatibility:** Keep existing methods during transition
2. **Feature Flags:** Use flags to switch between old/new implementations
3. **Comprehensive Testing:** Test all filter combinations
4. **Gradual Rollout:** Deploy to staging first, then production
5. **Rollback Plan:** Keep old code until new implementation is proven stable
